# the project aims to detect birds from videos

# the birds are extremely small due to long range and few pixels, cannot even be detected by human eyes

# the trick is using MOG2 background remover to detect moving pixels and then using findContours to generate boxes for targets,

# at last, we send those boxes to a simplified SORT tracker (just <PERSON>rman filter and IoU matching, no need for Re-ID module)